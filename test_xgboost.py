#!/usr/bin/env python3
"""
Simple XGBoost test
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import r2_score

print("Testing XGBoost...")

# Load small sample of data
train_data = pd.read_csv('task6_train_data_transformed.csv')
print(f"Data loaded: {train_data.shape}")

# Get features and target
feature_cols = [col for col in train_data.columns if col != 'Ksat']
X = train_data[feature_cols].iloc[:100]  # Small sample
y = train_data['Ksat'].iloc[:100]

print(f"Using {X.shape[0]} samples with {X.shape[1]} features")

# Test simple XGBoost model
model = xgb.XGBRegressor(
    n_estimators=10,
    max_depth=3,
    learning_rate=0.1,
    random_state=42,
    verbosity=0
)

print("Fitting model...")
model.fit(X, y)

print("Making predictions...")
y_pred = model.predict(X)

r2 = r2_score(y, y_pred)
print(f"R² score: {r2:.4f}")

print("XGBoost test successful!")
