- When running any python commands in the bash shell, you must first activate the project environment: `conda activate ml_24`
- see product requirement document from project_info.md
- see detail about task from task.md
- dont complete all task at once, user will say what task/tasks to be completed in a go. After each task change the task status. User will fill the 'finding' section of task manually. 
- When plotting data, do not show it, just save it to disk.