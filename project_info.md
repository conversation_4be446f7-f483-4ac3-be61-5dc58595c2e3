# Product Requirements Document (PRD)
## Soil Saturated Hydraulic Conductivity (Ksat) Prediction Using Machine Learning

### 1. Project Overview
This project aims to develop machine learning models to predict soil saturated hydraulic conductivity (Ksat) across the Continental United States (CONUS) using various soil properties, land use/land cover (LULC), vegetation indices, and environmental variables. Use Python (.py) for implementation.

### 2. Background & Motivation
- **Scientific Context**: Ksat is a critical soil hydraulic property that governs water movement through soil
- **Problem Statement**: Direct measurement of Ksat is time-consuming and expensive; accurate prediction models are needed
- **Research Gap**: Comparison of multiple ML methods (RF, SVM, XGBoost, ANN) for Ksat prediction at continental scale
- **Current Challenge**: Models experiencing overfitting during training phase

### 3. Data Description
- **Total Observations**: 5,770 soil samples
- **Target Variable**: Ksat (saturated hydraulic conductivity) - skewed distribution
- **Predictor Variables** (17 features):
  - Soil Properties: CEC, clay(%), sand(%), silt(%), soc(%), db (bulk density), depth, soil_acidity
  - Hydrological: PWP (permanent wilting point), FC (field capacity)
  - Environmental: NDVI, Pr (precipitation), T (temperature), Slope, TWI (topographic wetness index)
  - Categorical: LU (land use - numeric), Texture (string)
- **Data Quality**: No missing values
- **Data Format**: Excel file (data.xlsx) with headers

### 4. Technical Requirements

#### 4.1 Machine Learning Models
- Random Forest (RF)
- Support Vector Machine (SVM)
- XGBoost
- Artificial Neural Network (ANN)

#### 4.2 Data Preprocessing
- **Transformation**: Log or Box-Cox for skewed Ksat and other which has skewed distribution
- **Multicollinearity**: Check and remove highly correlated features
- **Encoding**: One-hot encoding for categorical variables (LU, Texture)
- **Scaling**: Z-score normalization for numerical features
- **Feature Selection**: PCA to select top 10 features

#### 4.3 Model Development
- **Data Split**: 60% training, 20% validation, 20% testing
- **Hyperparameter Optimization**: Grid search with 10-fold cross-validation
- **Overfitting Control**: Target R² difference between training and testing < 10%
- **Evaluation Metrics**: R², RMSE, MAE

### 5. Quality Standards
- **Academic Rigor**: Methods must meet publication standards
- **Reproducibility**: All processes must be documented and reproducible
- **Statistical Validity**: Proper validation and cross-validation procedures
- **Visualization**: Professional figures for manuscript inclusion

### 6. Deliverables
1. Optimized ML models with minimal overfitting
2. Model performance comparison table
3. Feature importance analysis
4. Complete methodology documentation for manuscript

### 7. Success Criteria
- Achieve R² difference between training and testing < 10% for all models
- Identify best performing model for Ksat prediction
- Generate scientifically sound results suitable for peer-reviewed publication
- Provide clear insights into important predictors of Ksat

### 8. Constraints & Assumptions
- Limited to available 5,770 observations for model training
- Results must be interpretable for soil science community

### 8. task.md
- for detail task see task.md
- status can be 'not_started' (not implemented); 'completed': completed
- after each task done, change the status from 'not_started' to 'completed'
- after each task done, complete the 'finding' section of task, which might be needed for other task.

### 8. file location
-  keep all figure inside figures folder
-  keep all task related code inside completed folder