#!/usr/bin/env python3
"""
Task 8: SVM Hyperparameter Optimization
Goal: Find optimal SVM parameters and control overfitting
"""

import pandas as pd
import numpy as np
import time
import json
import pickle
from sklearn.svm import SVR
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def load_datasets():
    """Load the transformed datasets from Task 6"""
    print("Loading transformed datasets from Task 6...")
    
    # Load datasets
    train_data = pd.read_csv('task6_train_data_transformed.csv')
    val_data = pd.read_csv('task6_validation_data_transformed.csv')
    test_data = pd.read_csv('task6_test_data_transformed.csv')
    
    print(f"Train data shape: {train_data.shape}")
    print(f"Validation data shape: {val_data.shape}")
    print(f"Test data shape: {test_data.shape}")
    
    # Separate features and target
    feature_cols = [col for col in train_data.columns if col != 'Ksat']
    
    X_train = train_data[feature_cols]
    y_train = train_data['Ksat']
    X_val = val_data[feature_cols]
    y_val = val_data['Ksat']
    X_test = test_data[feature_cols]
    y_test = test_data['Ksat']
    
    print(f"Number of features: {len(feature_cols)}")
    print(f"Features: {feature_cols}")
    
    # Display target statistics
    print(f"\nTransformed target variable (Ksat) statistics:")
    print(f"Train - Mean: {y_train.mean():.4f}, Std: {y_train.std():.4f}, Range: [{y_train.min():.4f}, {y_train.max():.4f}]")
    print(f"Val   - Mean: {y_val.mean():.4f}, Std: {y_val.std():.4f}, Range: [{y_val.min():.4f}, {y_val.max():.4f}]")
    print(f"Test  - Mean: {y_test.mean():.4f}, Std: {y_test.std():.4f}, Range: [{y_test.min():.4f}, {y_test.max():.4f}]")
    
    return X_train, y_train, X_val, y_val, X_test, y_test, feature_cols

def define_parameter_grid():
    """Define the parameter grid for SVM optimization"""
    param_grid = {
        'kernel': ['rbf', 'linear', 'poly'],
        'C': [0.1, 1, 10, 100],
        'gamma': ['scale', 'auto', 0.001, 0.01, 0.1]
    }
    
    print("Parameter grid defined:")
    for param, values in param_grid.items():
        print(f"  {param}: {values}")
    
    total_combinations = 1
    for values in param_grid.values():
        total_combinations *= len(values)
    print(f"Total parameter combinations: {total_combinations}")
    
    return param_grid

def perform_grid_search(X_train, y_train, param_grid):
    """Perform grid search with cross-validation"""
    print(f"\nPerforming Grid Search with 10-fold Cross-Validation...")
    print(f"Scoring metric: R²")
    print(f"Data: Box-Cox transformed features and target")
    
    # Initialize SVM
    svm = SVR()
    
    # Setup GridSearchCV
    grid_search = GridSearchCV(
        estimator=svm,
        param_grid=param_grid,
        cv=10,
        scoring='r2',
        n_jobs=-1,
        verbose=1
    )
    
    print("Starting grid search...")
    start_time = time.time()
    
    # Fit grid search
    grid_search.fit(X_train, y_train)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print(f"Grid search completed in {elapsed_time:.2f} seconds ({elapsed_time/60:.2f} minutes)")
    
    return grid_search

def evaluate_model(model, X_train, y_train, X_val, y_val, X_test, y_test):
    """Evaluate the model on different datasets"""
    print(f"\n{'='*60}")
    print(f"BEST MODEL EVALUATION (Box-Cox Transformed Data)")
    print(f"{'='*60}")
    
    # Best parameters
    print("Best parameters found:")
    for param, value in model.best_params_.items():
        print(f"  {param}: {value}")
    
    print(f"\nBest CV R² score: {model.best_score_:.4f}")
    
    # Get the best model
    best_model = model.best_estimator_
    
    # Predictions
    y_train_pred = best_model.predict(X_train)
    y_val_pred = best_model.predict(X_val)
    y_test_pred = best_model.predict(X_test)
    
    # Calculate metrics
    train_r2 = r2_score(y_train, y_train_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    train_mae = mean_absolute_error(y_train, y_train_pred)
    
    val_r2 = r2_score(y_val, y_val_pred)
    val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))
    val_mae = mean_absolute_error(y_val, y_val_pred)
    
    test_r2 = r2_score(y_test, y_test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    test_mae = mean_absolute_error(y_test, y_test_pred)
    
    print(f"\nEvaluating on different datasets:")
    print(f"Training   - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")
    print(f"Validation - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")
    print(f"Test       - R²: {test_r2:.4f}, RMSE: {test_rmse:.4f}, MAE: {test_mae:.4f}")
    
    # Overfitting analysis
    train_val_diff = train_r2 - val_r2
    train_test_diff = train_r2 - test_r2
    
    print(f"\nOverfitting Analysis:")
    print(f"R² difference (Train - Validation): {train_val_diff:.4f} ({train_val_diff*100:.2f}%)")
    print(f"R² difference (Train - Test): {train_test_diff:.4f} ({train_test_diff*100:.2f}%)")
    
    if train_val_diff > 0.1 or train_test_diff > 0.1:
        print(f"⚠️  WARNING: Model shows signs of overfitting (R² difference > 10%)")
        print(f"   Consider adjusting parameters:")
        print(f"   - Decrease C parameter (increase regularization)")
        print(f"   - Use different kernel")
        print(f"   - Adjust gamma parameter")
    else:
        print(f"✅ SUCCESS: Model shows acceptable overfitting (R² difference < 10%)")
    
    # Return metrics for saving
    metrics = {
        'best_params': model.best_params_,
        'best_cv_score': model.best_score_,
        'train_r2': train_r2,
        'train_rmse': train_rmse,
        'train_mae': train_mae,
        'val_r2': val_r2,
        'val_rmse': val_rmse,
        'val_mae': val_mae,
        'test_r2': test_r2,
        'test_rmse': test_rmse,
        'test_mae': test_mae,
        'train_val_diff': train_val_diff,
        'train_test_diff': train_test_diff,
        'overfitting_train_val': train_val_diff > 0.1,
        'overfitting_train_test': train_test_diff > 0.1
    }
    
    return best_model, metrics

def save_results(grid_search, best_model, metrics, feature_cols):
    """Save all results"""
    print(f"\nSaving results...")
    
    # Prepare results dictionary
    results = {
        'task': 'Task 8: SVM Hyperparameter Optimization',
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'data_info': {
            'transformation': 'Box-Cox transformed + StandardScaler',
            'n_features': len(feature_cols),
            'features': feature_cols
        },
        'grid_search_info': {
            'cv_folds': 10,
            'scoring': 'r2',
            'total_fits': len(grid_search.cv_results_['params']) * 10
        },
        'best_model': metrics,
        'parameter_grid': {
            'kernel': ['rbf', 'linear', 'poly'],
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1]
        }
    }
    
    # Save results as JSON
    with open('completed/task8_svm_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save best model
    with open('completed/task8_svm_best_model.pkl', 'wb') as f:
        pickle.dump(best_model, f)
    
    # Save grid search object
    with open('completed/task8_svm_grid_search.pkl', 'wb') as f:
        pickle.dump(grid_search, f)
    
    print("Results saved:")
    print("  - completed/task8_svm_results.json")
    print("  - completed/task8_svm_best_model.pkl")
    print("  - completed/task8_svm_grid_search.pkl")

def main():
    """Main execution function"""
    print("="*60)
    print("TASK 8: SVM HYPERPARAMETER OPTIMIZATION")
    print("(WITH BOX-COX TRANSFORMED DATA)")
    print("="*60)
    
    # Load datasets
    X_train, y_train, X_val, y_val, X_test, y_test, feature_cols = load_datasets()
    
    # Define parameter grid
    param_grid = define_parameter_grid()
    
    # Perform grid search
    grid_search = perform_grid_search(X_train, y_train, param_grid)
    
    # Evaluate best model
    best_model, metrics = evaluate_model(grid_search, X_train, y_train, X_val, y_val, X_test, y_test)
    
    # Save results
    save_results(grid_search, best_model, metrics, feature_cols)
    
    print(f"\n{'='*60}")
    print(f"TASK 8 COMPLETED SUCCESSFULLY!")
    print(f"(WITH BOX-COX TRANSFORMED DATA)")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
