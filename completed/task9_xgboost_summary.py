#!/usr/bin/env python3
"""
Task 9: XGBoost Hyperparameter Optimization - Summary and Results
This script documents the XGBoost hyperparameter optimization results
"""

import json
import pandas as pd

def print_xgboost_results():
    """Print comprehensive XGBoost optimization results"""
    
    print("="*70)
    print("TASK 9: XGBOOST HYPERPARAMETER OPTIMIZATION - RESULTS SUMMARY")
    print("="*70)
    
    # Load results
    with open('completed/task9_xgboost_results.json', 'r') as f:
        results = json.load(f)
    
    print("\n📊 OPTIMIZATION OVERVIEW:")
    print(f"• Parameter combinations tested: {results['parameter_combinations_tested']}")
    print(f"• Training time: {results['training_time_minutes']:.2f} minutes")
    print(f"• Best CV R² score: {results['best_cv_score']:.4f}")
    
    print("\n🎯 BEST PARAMETERS:")
    for param, value in results['best_params'].items():
        print(f"• {param}: {value}")
    
    print("\n📈 PERFORMANCE METRICS:")
    metrics = results['metrics']
    print(f"Training   - R²: {metrics['train']['r2']:.4f}, RMSE: {metrics['train']['rmse']:.2f}, MAE: {metrics['train']['mae']:.2f}")
    print(f"Validation - R²: {metrics['validation']['r2']:.4f}, RMSE: {metrics['validation']['rmse']:.2f}, MAE: {metrics['validation']['mae']:.2f}")
    print(f"Test       - R²: {metrics['test']['r2']:.4f}, RMSE: {metrics['test']['rmse']:.2f}, MAE: {metrics['test']['mae']:.2f}")
    
    print("\n🔍 OVERFITTING ANALYSIS:")
    overfitting = results['overfitting_analysis']
    print(f"• Train-Validation R² difference: {overfitting['train_validation_diff_percent']:.2f}%")
    print(f"• Train-Test R² difference: {overfitting['train_test_diff_percent']:.2f}%")
    print(f"• Threshold: {overfitting['threshold_percent']:.1f}%")
    
    if overfitting['overfitting_detected']:
        print("• Status: ⚠️  OVERFITTING DETECTED")
    else:
        print("• Status: ✅ OVERFITTING CONTROLLED")
    
    print("\n🏆 TOP FEATURE IMPORTANCE:")
    for feature, importance in results['feature_importance_top_5'].items():
        print(f"• {feature}: {importance:.3f} ({importance*100:.1f}%)")
    
    print("\n📋 PARAMETER GRID TESTED:")
    grid_details = results['grid_search_details']
    for param, values in grid_details.items():
        print(f"• {param}: {values}")
    
    print("\n🔬 CROSS-VALIDATION DETAILS:")
    cv_summary = results['cv_results_summary']
    print(f"• Mean CV score: {cv_summary['mean_test_score']:.4f} ± {cv_summary['std_test_score']:.4f}")
    print(f"• Mean train score: {cv_summary['mean_train_score']:.4f} ± {cv_summary['std_train_score']:.4f}")
    
    print("\n" + "="*70)
    print("TASK 9 ANALYSIS COMPLETE")
    print("="*70)

def compare_with_previous_models():
    """Compare XGBoost results with RF and SVM from previous tasks"""
    
    print("\n🔄 MODEL COMPARISON SUMMARY:")
    print("-" * 50)
    
    # XGBoost results (Task 9)
    xgb_test_r2 = 0.7812
    xgb_overfitting = 8.11
    
    # Random Forest results (Task 7) - from task.md findings
    rf_test_r2 = 0.7693
    rf_overfitting = 7.54
    
    # SVM results (Task 8) - from task.md findings  
    svm_test_r2 = 0.8007
    svm_overfitting = 7.90
    
    print(f"Random Forest  - Test R²: {rf_test_r2:.4f}, Overfitting: {rf_overfitting:.2f}%")
    print(f"SVM           - Test R²: {svm_test_r2:.4f}, Overfitting: {svm_overfitting:.2f}%")
    print(f"XGBoost       - Test R²: {xgb_test_r2:.4f}, Overfitting: {xgb_overfitting:.2f}%")
    
    print(f"\n🥇 RANKING BY TEST R²:")
    models = [
        ("SVM", svm_test_r2),
        ("XGBoost", xgb_test_r2), 
        ("Random Forest", rf_test_r2)
    ]
    
    for i, (model, score) in enumerate(sorted(models, key=lambda x: x[1], reverse=True), 1):
        print(f"{i}. {model}: {score:.4f}")
    
    print(f"\n📊 KEY INSIGHTS:")
    print(f"• SVM achieved the highest test R² ({svm_test_r2:.4f})")
    print(f"• XGBoost ranked 2nd with competitive performance ({xgb_test_r2:.4f})")
    print(f"• All models successfully controlled overfitting (<10% threshold)")
    print(f"• XGBoost showed good balance between performance and generalization")

if __name__ == "__main__":
    print_xgboost_results()
    compare_with_previous_models()
