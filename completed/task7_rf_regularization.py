#!/usr/bin/env python3
"""
Task 7: Random Forest Aggressive Regularization
Objective: Achieve overfitting < 10% through aggressive regularization
"""

import pandas as pd
import numpy as np
import time
import json
import pickle
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load the three transformed datasets from Task 6"""
    print("Loading transformed datasets from Task 6...")
    
    train_data = pd.read_csv('task6_train_data_transformed.csv')
    val_data = pd.read_csv('task6_validation_data_transformed.csv')
    test_data = pd.read_csv('task6_test_data_transformed.csv')
    
    print(f"Train data shape: {train_data.shape}")
    print(f"Validation data shape: {val_data.shape}")
    print(f"Test data shape: {test_data.shape}")
    
    # Separate features and target
    feature_cols = [col for col in train_data.columns if col != 'Ksat']
    
    X_train = train_data[feature_cols]
    y_train = train_data['Ksat']
    
    X_val = val_data[feature_cols]
    y_val = val_data['Ksat']
    
    X_test = test_data[feature_cols]
    y_test = test_data['Ksat']
    
    print(f"Number of features: {len(feature_cols)}")
    
    return X_train, y_train, X_val, y_val, X_test, y_test, feature_cols

def define_aggressive_regularization_grid():
    """Define very aggressive parameter grid to achieve < 10% overfitting"""
    # Much more aggressive parameters
    param_grid = {
        'n_estimators': [50, 100],           # Reduced further
        'max_depth': [8, 12, 15],            # Much shallower trees
        'min_samples_split': [20, 30, 50],   # Much higher minimum splits
        'min_samples_leaf': [10, 15, 20],    # Much higher minimum leaves
        'max_features': [0.3, 0.5, 'sqrt'],  # Fewer features per tree
        'max_samples': [0.7, 0.8, 0.9]       # Bootstrap sample size
    }
    
    print("Aggressive regularization parameter grid defined:")
    for param, values in param_grid.items():
        print(f"  {param}: {values}")
    
    total_combinations = 1
    for values in param_grid.values():
        total_combinations *= len(values)
    print(f"Total parameter combinations: {total_combinations}")
    
    return param_grid

def perform_grid_search(X_train, y_train, param_grid):
    """Perform grid search with aggressive regularization"""
    print("\nPerforming Grid Search with Aggressive Regularization...")
    print("Goal: Achieve overfitting < 10%")
    
    # Initialize Random Forest with additional regularization
    rf = RandomForestRegressor(
        random_state=42, 
        n_jobs=-1,
        bootstrap=True,
        oob_score=True,
        warm_start=False
    )
    
    # Setup GridSearchCV with 10-fold CV
    grid_search = GridSearchCV(
        estimator=rf,
        param_grid=param_grid,
        cv=10,
        scoring='r2',
        n_jobs=-1,
        verbose=1,
        return_train_score=True
    )
    
    # Record training time
    start_time = time.time()
    
    # Fit grid search
    print("Starting grid search...")
    grid_search.fit(X_train, y_train)
    
    end_time = time.time()
    training_time = end_time - start_time
    
    print(f"Grid search completed in {training_time:.2f} seconds ({training_time/60:.2f} minutes)")
    
    return grid_search, training_time

def evaluate_best_model(grid_search, X_train, y_train, X_val, y_val, X_test, y_test):
    """Evaluate the best model and check for overfitting"""
    print("\n" + "="*60)
    print("BEST MODEL EVALUATION (Aggressive Regularization)")
    print("="*60)
    
    best_model = grid_search.best_estimator_
    best_params = grid_search.best_params_
    best_cv_score = grid_search.best_score_
    
    print("Best parameters found:")
    for param, value in best_params.items():
        print(f"  {param}: {value}")
    
    print(f"\nBest CV R² score: {best_cv_score:.4f}")
    
    # Show OOB score
    if hasattr(best_model, 'oob_score_'):
        print(f"Out-of-bag R² score: {best_model.oob_score_:.4f}")
    
    # Evaluate on different sets
    print("\nEvaluating on different datasets:")
    
    # Training set
    y_train_pred = best_model.predict(X_train)
    train_r2 = r2_score(y_train, y_train_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    train_mae = mean_absolute_error(y_train, y_train_pred)
    
    # Validation set
    y_val_pred = best_model.predict(X_val)
    val_r2 = r2_score(y_val, y_val_pred)
    val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))
    val_mae = mean_absolute_error(y_val, y_val_pred)
    
    # Test set
    y_test_pred = best_model.predict(X_test)
    test_r2 = r2_score(y_test, y_test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    test_mae = mean_absolute_error(y_test, y_test_pred)
    
    print(f"Training   - R²: {train_r2:.4f}, RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}")
    print(f"Validation - R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}, MAE: {val_mae:.4f}")
    print(f"Test       - R²: {test_r2:.4f}, RMSE: {test_rmse:.4f}, MAE: {test_mae:.4f}")
    
    # Check for overfitting
    r2_diff_train_val = abs(train_r2 - val_r2)
    r2_diff_train_test = abs(train_r2 - test_r2)
    
    print(f"\nOverfitting Analysis:")
    print(f"R² difference (Train - Validation): {r2_diff_train_val:.4f} ({r2_diff_train_val*100:.2f}%)")
    print(f"R² difference (Train - Test): {r2_diff_train_test:.4f} ({r2_diff_train_test*100:.2f}%)")
    
    overfitting_threshold = 0.10  # 10%
    is_overfitting = r2_diff_train_val > overfitting_threshold or r2_diff_train_test > overfitting_threshold
    
    if is_overfitting:
        print(f"⚠️  WARNING: Model still shows signs of overfitting (R² difference > {overfitting_threshold*100:.0f}%)")
        print("   Consider even more aggressive regularization:")
        print("   - Further increase min_samples_leaf (25-30)")
        print("   - Further increase min_samples_split (50-100)")
        print("   - Reduce max_depth to 5-10")
        print("   - Reduce max_features to 0.2-0.3")
        print("   - Consider feature selection")
    else:
        print(f"✅ SUCCESS: Model shows acceptable overfitting (R² difference < {overfitting_threshold*100:.0f}%)")
    
    # Compare with previous results
    print(f"\n📊 Comparison with Previous Results:")
    print(f"   Previous CV R²: 0.7847 → Current CV R²: {best_cv_score:.4f}")
    print(f"   Previous Train-Val diff: 11.22% → Current: {r2_diff_train_val*100:.2f}%")
    print(f"   Previous Train-Test diff: 12.19% → Current: {r2_diff_train_test*100:.2f}%")
    
    # Feature importance analysis
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': best_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\nTop 10 Most Important Features:")
    for i, (_, row) in enumerate(feature_importance.head(10).iterrows(), 1):
        print(f"  {i:2d}. {row['feature']}: {row['importance']:.4f}")
    
    # Model complexity analysis
    print(f"\nModel Complexity Analysis:")
    print(f"  Number of trees: {best_model.n_estimators}")
    print(f"  Max depth: {best_model.max_depth}")
    print(f"  Min samples split: {best_model.min_samples_split}")
    print(f"  Min samples leaf: {best_model.min_samples_leaf}")
    print(f"  Max features: {best_model.max_features}")
    print(f"  Max samples: {best_model.max_samples}")
    
    return {
        'best_params': best_params,
        'best_cv_score': best_cv_score,
        'oob_score': getattr(best_model, 'oob_score_', None),
        'train_r2': train_r2,
        'train_rmse': train_rmse,
        'train_mae': train_mae,
        'val_r2': val_r2,
        'val_rmse': val_rmse,
        'val_mae': val_mae,
        'test_r2': test_r2,
        'test_rmse': test_rmse,
        'test_mae': test_mae,
        'r2_diff_train_val': r2_diff_train_val,
        'r2_diff_train_test': r2_diff_train_test,
        'is_overfitting': is_overfitting,
        'feature_importance': feature_importance.to_dict('records'),
        'best_model': best_model
    }

def save_results(results, grid_search, training_time, feature_cols):
    """Save results and model"""
    print("\nSaving results...")
    
    # Save detailed results
    results_to_save = {
        'task': 'Task 7: Random Forest Aggressive Regularization',
        'approach': 'Aggressive regularization to achieve < 10% overfitting',
        'regularization_techniques': [
            'Reduced n_estimators: [50, 100]',
            'Much shallower max_depth: [8, 12, 15]',
            'High min_samples_split: [20, 30, 50]',
            'High min_samples_leaf: [10, 15, 20]',
            'Reduced max_features: [0.3, 0.5, sqrt]',
            'Bootstrap sampling: max_samples [0.7, 0.8, 0.9]'
        ],
        'best_parameters': results['best_params'],
        'best_cv_score': results['best_cv_score'],
        'oob_score': results['oob_score'],
        'training_time_seconds': training_time,
        'training_time_minutes': training_time / 60,
        'performance_metrics': {
            'train': {
                'r2': results['train_r2'],
                'rmse': results['train_rmse'],
                'mae': results['train_mae']
            },
            'validation': {
                'r2': results['val_r2'],
                'rmse': results['val_rmse'],
                'mae': results['val_mae']
            },
            'test': {
                'r2': results['test_r2'],
                'rmse': results['test_rmse'],
                'mae': results['test_mae']
            }
        },
        'overfitting_analysis': {
            'r2_diff_train_val': results['r2_diff_train_val'],
            'r2_diff_train_test': results['r2_diff_train_test'],
            'is_overfitting': results['is_overfitting'],
            'target_achieved': not results['is_overfitting']
        },
        'comparison_with_previous': {
            'previous_cv_r2': 0.7847,
            'current_cv_r2': results['best_cv_score'],
            'previous_train_val_diff': 0.1122,
            'current_train_val_diff': results['r2_diff_train_val'],
            'previous_train_test_diff': 0.1219,
            'current_train_test_diff': results['r2_diff_train_test']
        },
        'feature_importance': results['feature_importance'],
        'feature_count': len(feature_cols),
        'cv_folds': 10,
        'random_state': 42
    }
    
    # Save results as JSON
    with open('completed/task7_rf_aggressive_regularization_results.json', 'w') as f:
        json.dump(results_to_save, f, indent=2)
    
    # Save best model
    with open('completed/task7_rf_aggressive_regularization_model.pkl', 'wb') as f:
        pickle.dump(results['best_model'], f)
    
    # Save grid search object
    with open('completed/task7_rf_aggressive_regularization_grid_search.pkl', 'wb') as f:
        pickle.dump(grid_search, f)
    
    print("Results saved:")
    print("  - completed/task7_rf_aggressive_regularization_results.json")
    print("  - completed/task7_rf_aggressive_regularization_model.pkl")
    print("  - completed/task7_rf_aggressive_regularization_grid_search.pkl")

def main():
    """Main function to execute aggressive regularization"""
    print("="*60)
    print("TASK 7: RANDOM FOREST AGGRESSIVE REGULARIZATION")
    print("GOAL: ACHIEVE OVERFITTING < 10%")
    print("="*60)
    
    # Load transformed data
    X_train, y_train, X_val, y_val, X_test, y_test, feature_cols = load_data()
    
    # Define aggressive regularization parameter grid
    param_grid = define_aggressive_regularization_grid()
    
    # Perform grid search
    grid_search, training_time = perform_grid_search(X_train, y_train, param_grid)
    
    # Evaluate best model
    results = evaluate_best_model(grid_search, X_train, y_train, X_val, y_val, X_test, y_test)
    
    # Save results
    save_results(results, grid_search, training_time, feature_cols)
    
    print("\n" + "="*60)
    if not results['is_overfitting']:
        print("🎉 SUCCESS: OVERFITTING TARGET ACHIEVED!")
        print("✅ R² differences are now < 10%")
    else:
        print("⚠️  OVERFITTING STILL PRESENT")
        print("   Further regularization needed")
    print("="*60)
    
    return results

if __name__ == "__main__":
    results = main()
