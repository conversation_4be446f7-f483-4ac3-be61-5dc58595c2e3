#!/usr/bin/env python3
"""
Task 2: Transformation Parameters
ML-Based Ksat Prediction Project

This file contains the transformation parameters determined in Task 2 for reproducibility.
Use these parameters to apply the same transformations in future tasks.
"""

import numpy as np
from scipy.stats import boxcox
import pandas as pd

# Transformation parameters determined in Task 2
TRANSFORMATION_PARAMETERS = {
    'Ksat': {
        'method': 'boxcox',
        'lambda': 0.2258,
        'original_skewness': 5.7863,
        'transformed_skewness': -0.1477,
        'improvement': 5.6386
    },
    'CEC': {
        'method': 'boxcox',
        'lambda': -0.7238,
        'original_skewness': 0.9952,
        'transformed_skewness': 0.0454,
        'improvement': 0.9498
    },
    'NDVI': {
        'method': 'boxcox',
        'lambda': 3.5969,
        'original_skewness': -1.1957,
        'transformed_skewness': -0.1250,
        'improvement': 1.0707
    },
    'Pr': {
        'method': 'boxcox',
        'lambda': -2.1443,
        'original_skewness': 0.8312,
        'transformed_skewness': -0.0433,
        'improvement': 0.7879
    },
    'Slope': {
        'method': 'boxcox',
        'lambda': 0.0281,
        'original_skewness': 1.7912,
        'transformed_skewness': -0.0056,
        'improvement': 1.7856
    },
    'db': {
        'method': 'boxcox',
        'lambda': 3.3270,
        'original_skewness': -1.1279,
        'transformed_skewness': 0.0283,
        'improvement': 1.0997
    },
    'soil_acidity': {
        'method': 'boxcox',
        'lambda': -1.1357,
        'original_skewness': 1.2540,
        'transformed_skewness': -0.0434,
        'improvement': 1.2106
    },
    'clay(%)': {
        'method': 'boxcox',
        'lambda': 0.1579,
        'original_skewness': 2.1746,
        'transformed_skewness': 0.0917,
        'improvement': 2.0830
    },
    'sand(%)': {
        'method': 'boxcox',
        'lambda': 3.8097,
        'original_skewness': -2.1707,
        'transformed_skewness': -0.7312,
        'improvement': 1.4395
    },
    'silt(%)': {
        'method': 'boxcox',
        'lambda': 0.2660,
        'original_skewness': 5.5906,
        'transformed_skewness': 0.1827,
        'improvement': 5.4079
    },
    'soc(%)': {
        'method': 'boxcox',
        'lambda': -0.0631,
        'original_skewness': 4.2431,
        'transformed_skewness': 0.0138,
        'improvement': 4.2293
    },
    'PWP': {
        'method': 'boxcox',
        'lambda': 0.0212,
        'original_skewness': 1.9213,
        'transformed_skewness': -0.0058,
        'improvement': 1.9155
    },
    'FC': {
        'method': 'boxcox',
        'lambda': 0.0331,
        'original_skewness': 1.0478,
        'transformed_skewness': -0.0100,
        'improvement': 1.0378
    }
}

# Variables that do not need transformation (|skewness| < 0.5)
NO_TRANSFORMATION_VARS = ['LU', 'TWI', 'T', 'depth']

def apply_boxcox_transformation(data, lambda_val):
    """
    Apply Box-Cox transformation with given lambda
    """
    if lambda_val == 0:
        return np.log(data)
    else:
        return (np.power(data, lambda_val) - 1) / lambda_val

def inverse_boxcox_transformation(transformed_data, lambda_val):
    """
    Apply inverse Box-Cox transformation
    """
    if lambda_val == 0:
        return np.exp(transformed_data)
    else:
        return np.power(lambda_val * transformed_data + 1, 1/lambda_val)

def transform_variable(data, var_name):
    """
    Transform a variable using the parameters determined in Task 2
    
    Parameters:
    -----------
    data : array-like
        Original data to transform
    var_name : str
        Variable name
    
    Returns:
    --------
    transformed_data : array-like
        Transformed data
    """
    if var_name in NO_TRANSFORMATION_VARS:
        print(f"No transformation needed for {var_name}")
        return data
    
    if var_name not in TRANSFORMATION_PARAMETERS:
        print(f"Warning: No transformation parameters found for {var_name}")
        return data
    
    params = TRANSFORMATION_PARAMETERS[var_name]
    
    if params['method'] == 'boxcox':
        # Ensure positive values
        if np.min(data) <= 0:
            data = data + abs(np.min(data)) + 1e-8
            print(f"Adjusted {var_name} to ensure positive values")
        
        transformed = apply_boxcox_transformation(data, params['lambda'])
        print(f"Applied Box-Cox transformation to {var_name} (λ={params['lambda']:.4f})")
        return transformed
    
    return data

def transform_dataframe(df):
    """
    Transform all variables in a dataframe using Task 2 parameters
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Original dataframe
    
    Returns:
    --------
    transformed_df : pandas.DataFrame
        Dataframe with transformed variables
    transformation_log : dict
        Log of transformations applied
    """
    transformed_df = df.copy()
    transformation_log = {}
    
    for col in df.columns:
        if col in TRANSFORMATION_PARAMETERS or col in NO_TRANSFORMATION_VARS:
            original_data = df[col].copy()
            transformed_data = transform_variable(original_data, col)
            transformed_df[col] = transformed_data
            
            if col in TRANSFORMATION_PARAMETERS:
                transformation_log[col] = {
                    'method': TRANSFORMATION_PARAMETERS[col]['method'],
                    'lambda': TRANSFORMATION_PARAMETERS[col]['lambda'],
                    'applied': True
                }
            else:
                transformation_log[col] = {
                    'method': 'none',
                    'applied': False
                }
    
    print(f"\nTransformation Summary:")
    print(f"- Transformed variables: {len([k for k, v in transformation_log.items() if v['applied']])}")
    print(f"- Untransformed variables: {len([k for k, v in transformation_log.items() if not v['applied']])}")
    
    return transformed_df, transformation_log

def get_transformation_info():
    """
    Get summary information about transformations
    """
    print("Task 2 Transformation Summary:")
    print("=" * 50)
    print(f"Total variables analyzed: {len(TRANSFORMATION_PARAMETERS) + len(NO_TRANSFORMATION_VARS)}")
    print(f"Variables transformed: {len(TRANSFORMATION_PARAMETERS)}")
    print(f"Variables unchanged: {len(NO_TRANSFORMATION_VARS)}")
    print(f"Transformation method: Box-Cox (all transformed variables)")
    
    print(f"\nTransformed variables:")
    for var, params in TRANSFORMATION_PARAMETERS.items():
        print(f"  {var:15s}: λ={params['lambda']:7.4f}, skew: {params['original_skewness']:6.3f} → {params['transformed_skewness']:6.3f}")
    
    print(f"\nUntransformed variables: {', '.join(NO_TRANSFORMATION_VARS)}")

if __name__ == "__main__":
    get_transformation_info()
