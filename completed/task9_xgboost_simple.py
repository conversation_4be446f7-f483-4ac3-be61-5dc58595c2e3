#!/usr/bin/env python3
"""
Task 9: XGBoost Hyperparameter Optimization - Simplified Version
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import cross_val_score
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import time
import json
import pickle
from itertools import product

def main():
    print("="*60)
    print("TASK 9: XGBOOST HYPERPARAMETER OPTIMIZATION")
    print("="*60)
    
    # Load data
    print("Loading datasets from Task 6...")
    train_data = pd.read_csv('task6_train_data_transformed.csv')
    val_data = pd.read_csv('task6_validation_data_transformed.csv')
    test_data = pd.read_csv('task6_test_data_transformed.csv')
    
    # Separate features and target
    feature_cols = [col for col in train_data.columns if col != 'Ksat']
    
    X_train = train_data[feature_cols]
    y_train = train_data['Ksat']
    X_val = val_data[feature_cols]
    y_val = val_data['Ksat']
    X_test = test_data[feature_cols]
    y_test = test_data['Ksat']
    
    print(f"Training set: {X_train.shape[0]} samples, {X_train.shape[1]} features")
    print(f"Validation set: {X_val.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")
    
    # Define parameter grid
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [3, 5, 7, 9],
        'learning_rate': [0.01, 0.1, 0.3],
        'subsample': [0.7, 0.8, 0.9]
    }
    
    print(f"\nParameter grid: {len(list(product(*param_grid.values())))} combinations")
    
    # Manual grid search
    best_score = -np.inf
    best_params = None
    best_model = None
    
    start_time = time.time()
    
    param_combinations = list(product(*param_grid.values()))
    param_names = list(param_grid.keys())
    
    print("\nStarting grid search...")
    
    for i, param_combo in enumerate(param_combinations):
        params = dict(zip(param_names, param_combo))
        
        print(f"Testing combination {i+1}/{len(param_combinations)}: {params}")
        
        # Create model
        model = xgb.XGBRegressor(
            random_state=42,
            verbosity=0,
            **params
        )
        
        # Cross-validation
        try:
            cv_scores = cross_val_score(model, X_train, y_train, cv=10, scoring='r2')
            mean_score = cv_scores.mean()
            std_score = cv_scores.std()
            
            print(f"  CV R²: {mean_score:.4f} ± {std_score:.4f}")
            
            if mean_score > best_score:
                best_score = mean_score
                best_params = params
                best_model = model
                print(f"  *** NEW BEST SCORE! ***")
                
        except Exception as e:
            print(f"  Error: {e}")
            continue
    
    # Train best model
    print(f"\nBest parameters: {best_params}")
    print(f"Best CV R² score: {best_score:.4f}")
    
    best_model.fit(X_train, y_train)
    
    # Evaluate
    y_train_pred = best_model.predict(X_train)
    y_val_pred = best_model.predict(X_val)
    y_test_pred = best_model.predict(X_test)
    
    train_r2 = r2_score(y_train, y_train_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    train_mae = mean_absolute_error(y_train, y_train_pred)
    
    val_r2 = r2_score(y_val, y_val_pred)
    val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))
    val_mae = mean_absolute_error(y_val, y_val_pred)
    
    test_r2 = r2_score(y_test, y_test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    test_mae = mean_absolute_error(y_test, y_test_pred)
    
    end_time = time.time()
    training_time = end_time - start_time
    
    print(f"\nTraining completed in {training_time:.2f} seconds ({training_time/60:.2f} minutes)")
    
    print(f"\nPerformance Metrics:")
    print(f"Training   - R²: {train_r2:.4f}, RMSE: {train_rmse:.2f}, MAE: {train_mae:.2f}")
    print(f"Validation - R²: {val_r2:.4f}, RMSE: {val_rmse:.2f}, MAE: {val_mae:.2f}")
    print(f"Test       - R²: {test_r2:.4f}, RMSE: {test_rmse:.2f}, MAE: {test_mae:.2f}")
    
    # Overfitting analysis
    train_val_diff = (train_r2 - val_r2) * 100
    train_test_diff = (train_r2 - test_r2) * 100
    
    print(f"\nOverfitting Analysis:")
    print(f"Train-Validation R² difference: {train_val_diff:.2f}%")
    print(f"Train-Test R² difference: {train_test_diff:.2f}%")
    
    if train_val_diff > 10 or train_test_diff > 10:
        print("⚠️  OVERFITTING DETECTED!")
        overfitting_detected = True
    else:
        print("✅ OVERFITTING CONTROLLED!")
        overfitting_detected = False
    
    # Save results
    results = {
        'best_params': best_params,
        'best_cv_score': best_score,
        'training_time_seconds': training_time,
        'training_time_minutes': training_time / 60,
        'metrics': {
            'train': {'r2': train_r2, 'rmse': train_rmse, 'mae': train_mae},
            'validation': {'r2': val_r2, 'rmse': val_rmse, 'mae': val_mae},
            'test': {'r2': test_r2, 'rmse': test_rmse, 'mae': test_mae}
        },
        'overfitting_analysis': {
            'overfitting_detected': overfitting_detected,
            'train_validation_diff_percent': train_val_diff,
            'train_test_diff_percent': train_test_diff,
            'threshold_percent': 10.0
        }
    }
    
    # Save files
    with open('completed/task9_xgboost_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    with open('completed/task9_xgboost_best_model.pkl', 'wb') as f:
        pickle.dump(best_model, f)
    
    print("\nResults saved:")
    print("- completed/task9_xgboost_results.json")
    print("- completed/task9_xgboost_best_model.pkl")
    
    print("\n" + "="*60)
    print("TASK 9 COMPLETED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()
