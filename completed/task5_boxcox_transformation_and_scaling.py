#!/usr/bin/env python3
"""
Task 5: Box-Cox Transformation and Feature Scaling
Objective: Apply Box-Cox transformations from Task 2 and then standardize numerical features
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.preprocessing import StandardScaler
# Transformation parameters from Task 2
TRANSFORMATION_PARAMETERS = {
    'Ksat': {'method': 'boxcox', 'lambda': 0.2258},
    'CEC': {'method': 'boxcox', 'lambda': -0.7238},
    'NDVI': {'method': 'boxcox', 'lambda': 3.5969},
    'Pr': {'method': 'boxcox', 'lambda': -2.1443},
    'Slope': {'method': 'boxcox', 'lambda': 0.0281},
    'db': {'method': 'boxcox', 'lambda': 3.3270},
    'soil_acidity': {'method': 'boxcox', 'lambda': -1.1357},
    'clay(%)': {'method': 'boxcox', 'lambda': 0.1579},
    'sand(%)': {'method': 'boxcox', 'lambda': 3.8097},
    'silt(%)': {'method': 'boxcox', 'lambda': 0.2660},
    'soc(%)': {'method': 'boxcox', 'lambda': -0.0631},
    'FC': {'method': 'boxcox', 'lambda': 0.0331}
}

def apply_boxcox_transformation(data, lambda_val):
    """Apply Box-Cox transformation with given lambda"""
    if lambda_val == 0:
        return np.log(data)
    else:
        return (np.power(data, lambda_val) - 1) / lambda_val

def load_encoded_data():
    """Load the encoded data from Task 4"""
    print("=== Task 5: Box-Cox Transformation and Feature Scaling ===")
    print("Loading encoded data from task4_encoded_data.csv...")
    
    data = pd.read_csv('task4_encoded_data.csv')
    print(f"Data shape: {data.shape}")
    
    # Verify target and features
    target_col = 'Ksat'
    feature_cols = [col for col in data.columns if col != target_col]
    
    print(f"Target variable: {target_col}")
    print(f"Features shape: ({data.shape[0]}, {len(feature_cols)})")
    
    return data, target_col, feature_cols

def apply_boxcox_transformations(data):
    """Apply Box-Cox transformations to the 12 variables identified in Task 2"""
    print("\nApplying Box-Cox transformations...")
    
    # Variables to transform (excluding PWP which was removed in Task 3)
    variables_to_transform = [
        'Ksat', 'CEC', 'NDVI', 'Pr', 'Slope', 'db', 
        'soil_acidity', 'clay(%)', 'sand(%)', 'silt(%)', 'soc(%)', 'FC'
    ]
    
    transformed_data = data.copy()
    transformation_log = {}
    
    print(f"Variables to transform ({len(variables_to_transform)}):")
    for i, var in enumerate(variables_to_transform, 1):
        print(f"  {i:2d}. {var}")
    
    # Apply transformations
    for var in variables_to_transform:
        if var in data.columns and var in TRANSFORMATION_PARAMETERS:
            params = TRANSFORMATION_PARAMETERS[var]
            original_data = data[var].copy()
            
            # Ensure positive values for Box-Cox
            if np.min(original_data) <= 0:
                shift_value = abs(np.min(original_data)) + 1e-8
                original_data = original_data + shift_value
                print(f"  {var}: Shifted by {shift_value:.6f} to ensure positive values")
            
            # Apply Box-Cox transformation
            lambda_val = params['lambda']
            transformed_values = apply_boxcox_transformation(original_data, lambda_val)
            transformed_data[var] = transformed_values
            
            # Calculate skewness improvement
            original_skew = data[var].skew()
            transformed_skew = transformed_values.skew()
            
            transformation_log[var] = {
                'method': 'boxcox',
                'lambda': lambda_val,
                'original_skewness': original_skew,
                'transformed_skewness': transformed_skew,
                'improvement': abs(original_skew) - abs(transformed_skew),
                'applied': True
            }
            
            print(f"  {var}: λ={lambda_val:.4f}, skew: {original_skew:.3f} → {transformed_skew:.3f}")
        
        elif var in data.columns:
            print(f"  {var}: No transformation parameters found")
            transformation_log[var] = {'applied': False, 'reason': 'no_parameters'}
        else:
            print(f"  {var}: Not found in dataset")
            transformation_log[var] = {'applied': False, 'reason': 'not_found'}
    
    # Variables that don't need transformation
    no_transform_vars = ['TWI', 'T ', 'depth']  # Note: 'T ' has a space
    print(f"\nVariables not requiring transformation:")
    for var in no_transform_vars:
        if var in data.columns:
            print(f"  {var}: Acceptable skewness (|skew| < 0.5)")
            transformation_log[var] = {'applied': False, 'reason': 'acceptable_skewness'}
    
    print(f"\nTransformation Summary:")
    applied_count = sum(1 for v in transformation_log.values() if v.get('applied', False))
    print(f"  Variables transformed: {applied_count}")
    print(f"  Variables unchanged: {len(transformation_log) - applied_count}")
    
    return transformed_data, transformation_log

def identify_feature_types(data, target_col):
    """Identify numerical and categorical features"""
    print("\nIdentifying feature types...")
    
    feature_cols = [col for col in data.columns if col != target_col]
    
    # Numerical features (continuous variables)
    numerical_features = []
    categorical_features = []
    
    for col in feature_cols:
        if col.startswith('LU_') or col.startswith('Texture_'):
            categorical_features.append(col)
        else:
            numerical_features.append(col)
    
    print(f"\nNumerical features to scale ({len(numerical_features)}):")
    for i, feature in enumerate(numerical_features, 1):
        print(f"  {i:2d}. {feature}")
    
    print(f"\nCategorical features (one-hot encoded, no scaling needed) ({len(categorical_features)}):")
    lu_features = [f for f in categorical_features if f.startswith('LU_')]
    texture_features = [f for f in categorical_features if f.startswith('Texture_')]
    print(f"  - LU features: {len(lu_features)}")
    print(f"  - Texture features: {len(texture_features)}")
    
    return numerical_features, categorical_features

def apply_feature_scaling(data, numerical_features, target_col):
    """Apply StandardScaler to numerical features"""
    print(f"\nApplying StandardScaler to {len(numerical_features)} numerical features...")
    
    scaled_data = data.copy()
    
    # Initialize scaler
    scaler = StandardScaler()
    
    # Fit and transform numerical features
    scaled_values = scaler.fit_transform(data[numerical_features])
    scaled_data[numerical_features] = scaled_values
    
    print("Scaling completed!")
    
    # Verify scaling
    print(f"\nScaling verification for numerical features:")
    means = scaled_data[numerical_features].mean()
    stds = scaled_data[numerical_features].std()
    print(f"Mean range: [{means.min():.6f}, {means.max():.6f}]")
    print(f"Std range: [{stds.min():.6f}, {stds.max():.6f}]")
    
    return scaled_data, scaler

def save_results(transformed_scaled_data, transformation_log, scaler, numerical_features, categorical_features):
    """Save all results and parameters"""
    print(f"\nFinal transformed and scaled dataset shape: {transformed_scaled_data.shape}")
    
    # Save the transformed and scaled dataset
    output_file = 'task5_transformed_scaled_data.csv'
    transformed_scaled_data.to_csv(output_file, index=False)
    print(f"Transformed and scaled dataset saved as: {output_file}")
    
    # Save scaler object
    scaler_file = 'completed/task5_scaler.pkl'
    with open(scaler_file, 'wb') as f:
        pickle.dump(scaler, f)
    print(f"Scaler object saved as: {scaler_file}")
    
    # Save comprehensive information
    info = {
        'task': 'Task 5: Box-Cox Transformation and Feature Scaling',
        'dataset_shape': transformed_scaled_data.shape,
        'transformations_applied': transformation_log,
        'numerical_features': numerical_features,
        'categorical_features': categorical_features,
        'scaling_method': 'StandardScaler',
        'output_file': output_file,
        'scaler_file': scaler_file
    }
    
    info_file = 'completed/task5_transformation_scaling_info.json'
    with open(info_file, 'w') as f:
        json.dump(info, f, indent=2, default=str)
    print(f"Transformation and scaling information saved as: {info_file}")

def main():
    """Main function to execute Task 5"""
    # Load encoded data
    data, target_col, feature_cols = load_encoded_data()
    
    # Apply Box-Cox transformations
    transformed_data, transformation_log = apply_boxcox_transformations(data)
    
    # Identify feature types
    numerical_features, categorical_features = identify_feature_types(transformed_data, target_col)
    
    # Apply feature scaling
    transformed_scaled_data, scaler = apply_feature_scaling(transformed_data, numerical_features, target_col)
    
    # Save results
    save_results(transformed_scaled_data, transformation_log, scaler, numerical_features, categorical_features)
    
    print("\n=== Task 5 Summary ===")
    print(f"✓ Loaded encoded data: {data.shape}")
    applied_transformations = sum(1 for v in transformation_log.values() if v.get('applied', False))
    print(f"✓ Applied Box-Cox transformations to {applied_transformations} variables")
    print(f"✓ Identified {len(numerical_features)} numerical features for scaling")
    print(f"✓ Identified {len(categorical_features)} categorical features (no scaling)")
    print(f"✓ Applied StandardScaler to numerical features")
    print(f"✓ Saved transformed and scaled dataset: task5_transformed_scaled_data.csv")
    print(f"✓ Saved scaler object: completed/task5_scaler.pkl")
    print(f"✓ Saved transformation info: completed/task5_transformation_scaling_info.json")
    print("Task 5 completed successfully!")
    
    return transformed_scaled_data, transformation_log, scaler

if __name__ == "__main__":
    result = main()
