XGBoost Model Information - Task 9
=====================================

Model Type: XGBoost Regressor
Objective: Saturated Hydraulic Conductivity (Ksat) Prediction

Best Hyperparameters:
- n_estimators: 200
- max_depth: 5  
- learning_rate: 0.1
- subsample: 0.8
- random_state: 42

Performance Metrics:
- Cross-Validation R²: 0.7856 ± 0.0234
- Training R²: 0.8623 (RMSE: 2.17, MAE: 1.34)
- Validation R²: 0.7934 (RMSE: 2.67, MAE: 1.95)
- Test R²: 0.7812 (RMSE: 2.86, MAE: 2.01)

Overfitting Analysis:
- Train-Validation difference: 6.89% ✅
- Train-Test difference: 8.11% ✅
- Both below 10% threshold - overfitting controlled

Training Details:
- Parameter combinations tested: 108
- Training time: 3.09 minutes
- 10-fold cross-validation used
- Box-Cox transformed features

Top Feature Importance:
1. clay(%): 28.5%
2. FC (Field Capacity): 24.1%
3. sand(%): 15.6%
4. db (Bulk Density): 9.8%
5. soc(%): 6.7%

Model Ranking:
- 2nd best among all models tested
- Excellent balance of performance and generalization
- Competitive with SVM (1st place: 0.8007 R²)
- Outperformed Random Forest (3rd place: 0.7693 R²)

Files Generated:
- task9_xgboost_results.json: Complete results
- task9_xgboost_summary.py: Analysis script
- task9_xgboost_hyperparameter_optimization.py: Main implementation
