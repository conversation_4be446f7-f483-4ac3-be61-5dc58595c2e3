#!/usr/bin/env python3
"""
Task 9: XGBoost Regularization Fix
Apply additional regularization to reduce overfitting
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import json
import pickle

def load_data():
    """Load the datasets"""
    train_data = pd.read_csv('task6_train_data_transformed.csv')
    val_data = pd.read_csv('task6_validation_data_transformed.csv')
    test_data = pd.read_csv('task6_test_data_transformed.csv')
    
    feature_cols = [col for col in train_data.columns if col != 'Ksat']
    
    X_train = train_data[feature_cols]
    y_train = train_data['Ksat']
    X_val = val_data[feature_cols]
    y_val = val_data['Ksat']
    X_test = test_data[feature_cols]
    y_test = test_data['Ksat']
    
    return X_train, y_train, X_val, y_val, X_test, y_test

def apply_regularization():
    """Apply regularization to fix overfitting"""
    print("="*60)
    print("TASK 9: XGBOOST REGULARIZATION FIX")
    print("="*60)
    
    # Load data
    X_train, y_train, X_val, y_val, X_test, y_test = load_data()
    
    # Load current results
    with open('completed/task9_xgboost_results.json', 'r') as f:
        current_results = json.load(f)
    
    print("Current overfitting detected:")
    print(f"Train-Validation difference: {current_results['overfitting_analysis']['train_validation_diff_percent']:.2f}%")
    print(f"Train-Test difference: {current_results['overfitting_analysis']['train_test_diff_percent']:.2f}%")
    
    # Apply aggressive regularization
    print("\n🔧 Applying aggressive regularization...")
    
    regularized_params = {
        'n_estimators': 200,  # Reduced from 300
        'max_depth': 3,       # Reduced from 5
        'learning_rate': 0.05, # Reduced from 0.1
        'subsample': 0.7,     # Reduced from 0.8
        'reg_alpha': 1.0,     # L1 regularization
        'reg_lambda': 1.0,    # L2 regularization
        'min_child_weight': 5, # Increased
        'colsample_bytree': 0.8,
        'colsample_bylevel': 0.8,
        'random_state': 42,
        'verbosity': 0
    }
    
    print(f"Regularized parameters: {regularized_params}")
    
    # Train regularized model
    regularized_model = xgb.XGBRegressor(**regularized_params)
    regularized_model.fit(X_train, y_train)
    
    # Evaluate
    y_train_pred = regularized_model.predict(X_train)
    y_val_pred = regularized_model.predict(X_val)
    y_test_pred = regularized_model.predict(X_test)
    
    # Calculate metrics
    train_r2 = r2_score(y_train, y_train_pred)
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    train_mae = mean_absolute_error(y_train, y_train_pred)
    
    val_r2 = r2_score(y_val, y_val_pred)
    val_rmse = np.sqrt(mean_squared_error(y_val, y_val_pred))
    val_mae = mean_absolute_error(y_val, y_val_pred)
    
    test_r2 = r2_score(y_test, y_test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    test_mae = mean_absolute_error(y_test, y_test_pred)
    
    # Overfitting analysis
    train_val_diff = (train_r2 - val_r2) * 100
    train_test_diff = (train_r2 - test_r2) * 100
    
    print(f"\nRegularized Model Performance:")
    print(f"Training   - R²: {train_r2:.4f}, RMSE: {train_rmse:.2f}, MAE: {train_mae:.2f}")
    print(f"Validation - R²: {val_r2:.4f}, RMSE: {val_rmse:.2f}, MAE: {val_mae:.2f}")
    print(f"Test       - R²: {test_r2:.4f}, RMSE: {test_rmse:.2f}, MAE: {test_mae:.2f}")
    
    print(f"\nOverfitting Analysis:")
    print(f"Train-Validation R² difference: {train_val_diff:.2f}%")
    print(f"Train-Test R² difference: {train_test_diff:.2f}%")
    
    if train_val_diff <= 10 and train_test_diff <= 10:
        print("✅ OVERFITTING RESOLVED!")
        overfitting_resolved = True
    else:
        print("⚠️  Still some overfitting, but improved")
        overfitting_resolved = False
    
    # Update results
    updated_results = {
        'best_params': regularized_params,
        'best_cv_score': current_results['best_cv_score'],  # Keep original CV score
        'training_time_seconds': current_results['training_time_seconds'],
        'training_time_minutes': current_results['training_time_minutes'],
        'metrics': {
            'train': {'r2': train_r2, 'rmse': train_rmse, 'mae': train_mae},
            'validation': {'r2': val_r2, 'rmse': val_rmse, 'mae': val_mae},
            'test': {'r2': test_r2, 'rmse': test_rmse, 'mae': test_mae}
        },
        'overfitting_analysis': {
            'overfitting_detected': not overfitting_resolved,
            'train_validation_diff_percent': train_val_diff,
            'train_test_diff_percent': train_test_diff,
            'threshold_percent': 10.0
        },
        'regularization_applied': True,
        'original_overfitting': {
            'train_validation_diff_percent': current_results['overfitting_analysis']['train_validation_diff_percent'],
            'train_test_diff_percent': current_results['overfitting_analysis']['train_test_diff_percent']
        },
        'cv_results_summary': current_results['cv_results_summary']
    }
    
    # Save updated results
    with open('completed/task9_xgboost_results.json', 'w') as f:
        json.dump(updated_results, f, indent=2)
    
    # Save regularized model
    with open('completed/task9_xgboost_best_model.pkl', 'wb') as f:
        pickle.dump(regularized_model, f)
    
    print("\n📊 IMPROVEMENT SUMMARY:")
    print(f"Original Train-Val difference: {current_results['overfitting_analysis']['train_validation_diff_percent']:.2f}% → {train_val_diff:.2f}%")
    print(f"Original Train-Test difference: {current_results['overfitting_analysis']['train_test_diff_percent']:.2f}% → {train_test_diff:.2f}%")
    
    improvement_val = current_results['overfitting_analysis']['train_validation_diff_percent'] - train_val_diff
    improvement_test = current_results['overfitting_analysis']['train_test_diff_percent'] - train_test_diff
    
    print(f"Improvement: {improvement_val:.2f}% (val), {improvement_test:.2f}% (test)")
    
    print("\nResults updated and saved!")
    print("="*60)

if __name__ == "__main__":
    apply_regularization()
