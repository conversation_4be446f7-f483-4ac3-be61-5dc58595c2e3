#!/usr/bin/env python3
"""
Task 6: Data Splitting (with Box-Cox Transformed Data)
Objective: Create train/validation/test sets using the Box-Cox transformed and scaled data
"""

import pandas as pd
import numpy as np
import json
from sklearn.model_selection import train_test_split

def load_transformed_data():
    """Load the Box-Cox transformed and scaled data from Task 5"""
    print("=== Task 6: Data Splitting (Transformed Data) ===")
    print("Random seed set to: 42")
    print("Loading transformed and scaled data from task5_transformed_scaled_data.csv...")
    
    data = pd.read_csv('task5_transformed_scaled_data.csv')
    print(f"Data shape: {data.shape}")
    
    # Verify target and features
    target_col = 'Ksat'
    feature_cols = [col for col in data.columns if col != target_col]
    
    print(f"Target variable: {target_col}")
    print(f"Features shape: ({data.shape[0]}, {len(feature_cols)})")
    
    # Target statistics
    print(f"Target statistics:")
    print(f"  Mean: {data[target_col].mean():.4f}")
    print(f"  Std: {data[target_col].std():.4f}")
    print(f"  Min: {data[target_col].min():.4f}")
    print(f"  Max: {data[target_col].max():.4f}")
    
    return data, target_col, feature_cols

def split_data(data, target_col, feature_cols, random_state=42):
    """Split data into train/validation/test sets (60%/20%/20%)"""
    print(f"\nSplitting data with random_state={random_state}:")
    print("Step 1: 60% train, 40% temporary")
    print("Step 2: Split 40% temporary into 20% validation + 20% test")
    
    # Separate features and target
    X = data[feature_cols]
    y = data[target_col]
    
    # First split: 60% train, 40% temporary
    X_train, X_temp, y_train, y_temp = train_test_split(
        X, y, test_size=0.4, random_state=random_state, stratify=None
    )
    
    # Second split: 20% validation, 20% test (from the 40% temporary)
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=random_state, stratify=None
    )
    
    # Verify split sizes
    total_samples = len(data)
    train_samples = len(X_train)
    val_samples = len(X_val)
    test_samples = len(X_test)
    
    print(f"\nSplit verification:")
    print(f"Total samples: {total_samples}")
    print(f"Train samples: {train_samples} ({train_samples/total_samples*100:.1f}%)")
    print(f"Validation samples: {val_samples} ({val_samples/total_samples*100:.1f}%)")
    print(f"Test samples: {test_samples} ({test_samples/total_samples*100:.1f}%)")
    print(f"Sum: {train_samples + val_samples + test_samples} (should equal {total_samples})")
    
    # Create complete datasets (features + target)
    train_data = pd.concat([X_train, y_train], axis=1)
    val_data = pd.concat([X_val, y_val], axis=1)
    test_data = pd.concat([X_test, y_test], axis=1)
    
    print(f"\nDataset shapes:")
    print(f"Train dataset: {train_data.shape}")
    print(f"Validation dataset: {val_data.shape}")
    print(f"Test dataset: {test_data.shape}")
    
    # Target distribution across splits
    print(f"\nTarget distribution across splits:")
    print(f"Train - Mean: {y_train.mean():.4f}, Std: {y_train.std():.4f}")
    print(f"Val   - Mean: {y_val.mean():.4f}, Std: {y_val.std():.4f}")
    print(f"Test  - Mean: {y_test.mean():.4f}, Std: {y_test.std():.4f}")
    
    return train_data, val_data, test_data

def save_datasets(train_data, val_data, test_data):
    """Save the three datasets as CSV files"""
    print(f"\nSaving datasets...")
    
    # Save datasets
    train_file = 'task6_train_data_transformed.csv'
    val_file = 'task6_validation_data_transformed.csv'
    test_file = 'task6_test_data_transformed.csv'
    
    train_data.to_csv(train_file, index=False)
    val_data.to_csv(val_file, index=False)
    test_data.to_csv(test_file, index=False)
    
    print(f"Train data saved as: {train_file}")
    print(f"Validation data saved as: {val_file}")
    print(f"Test data saved as: {test_file}")
    
    # Save split information
    split_info = {
        'task': 'Task 6: Data Splitting (Box-Cox Transformed)',
        'random_state': 42,
        'total_samples': len(train_data) + len(val_data) + len(test_data),
        'train_samples': len(train_data),
        'validation_samples': len(val_data),
        'test_samples': len(test_data),
        'train_percentage': len(train_data) / (len(train_data) + len(val_data) + len(test_data)) * 100,
        'validation_percentage': len(val_data) / (len(train_data) + len(val_data) + len(test_data)) * 100,
        'test_percentage': len(test_data) / (len(train_data) + len(val_data) + len(test_data)) * 100,
        'target_distribution': {
            'train': {
                'mean': float(train_data['Ksat'].mean()),
                'std': float(train_data['Ksat'].std()),
                'min': float(train_data['Ksat'].min()),
                'max': float(train_data['Ksat'].max())
            },
            'validation': {
                'mean': float(val_data['Ksat'].mean()),
                'std': float(val_data['Ksat'].std()),
                'min': float(val_data['Ksat'].min()),
                'max': float(val_data['Ksat'].max())
            },
            'test': {
                'mean': float(test_data['Ksat'].mean()),
                'std': float(test_data['Ksat'].std()),
                'min': float(test_data['Ksat'].min()),
                'max': float(test_data['Ksat'].max())
            }
        },
        'files': {
            'train': train_file,
            'validation': val_file,
            'test': test_file
        }
    }
    
    info_file = 'completed/task6_split_info_transformed.json'
    with open(info_file, 'w') as f:
        json.dump(split_info, f, indent=2)
    print(f"Split information saved as: {info_file}")

def main():
    """Main function to execute Task 6 with transformed data"""
    # Load transformed data
    data, target_col, feature_cols = load_transformed_data()
    
    # Split data
    train_data, val_data, test_data = split_data(data, target_col, feature_cols)
    
    # Save datasets
    save_datasets(train_data, val_data, test_data)
    
    print(f"\n=== Task 6 Summary ===")
    print(f"✓ Set random seed: 42")
    print(f"✓ Loaded transformed and scaled data: {data.shape}")
    print(f"✓ Split data: 60% train ({len(train_data)}), 20% val ({len(val_data)}), 20% test ({len(test_data)})")
    print(f"✓ Saved train dataset: task6_train_data_transformed.csv")
    print(f"✓ Saved validation dataset: task6_validation_data_transformed.csv")
    print(f"✓ Saved test dataset: task6_test_data_transformed.csv")
    print(f"✓ Saved split info: completed/task6_split_info_transformed.json")
    print("Task 6 completed successfully!")
    
    return train_data, val_data, test_data

if __name__ == "__main__":
    result = main()
