#!/usr/bin/env python3
"""
Task 2: Target Variable Transformation
ML-Based Ksat Prediction Project

This script performs transformation analysis on all skewed variables (Ksat and others) to address skewness.
Tests both log and Box-Cox transformations and selects the best approach for each variable.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import boxcox, shapiro
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

def load_data(file_path):
    """
    Load the dataset and identify all variables needing transformation
    """
    print("="*60)
    print("TASK 2: SKEWED VARIABLES TRANSFORMATION")
    print("="*60)

    print("\n1. Loading data...")
    df = pd.read_excel(file_path)
    print(f"✓ Data loaded: {df.shape}")

    # Identify Ksat column (target variable)
    ksat_col = None
    for col in df.columns:
        if 'ksat' in col.lower() or 'k_sat' in col.lower():
            ksat_col = col
            break

    if ksat_col is None:
        # If not found by name, assume first column
        print("⚠ Ksat not found by name. Checking column patterns...")
        print("Available columns:", list(df.columns))
        ksat_col = df.columns[0]  # Assume first column is target
        print(f"Assuming '{ksat_col}' is the target variable")

    print(f"✓ Target variable identified: {ksat_col}")

    return df, ksat_col

def identify_skewed_variables(df, skewness_threshold=0.5):
    """
    Identify all variables that need transformation based on skewness
    """
    print("\n2. Identifying skewed variables...")

    numerical_cols = df.select_dtypes(include=[np.number]).columns
    skewed_vars = {}

    print(f"Analyzing {len(numerical_cols)} numerical variables for skewness...")
    print("-" * 60)

    for col in numerical_cols:
        # Handle potential zeros or negative values
        data = df[col].copy()
        if data.min() <= 0:
            # Add small constant to handle zeros/negatives for log transformation
            data = data + abs(data.min()) + 1e-8

        skewness = stats.skew(data)
        kurtosis = stats.kurtosis(data)

        print(f"{col:15s}: skewness={skewness:7.3f}, kurtosis={kurtosis:7.3f}", end="")

        if abs(skewness) > skewness_threshold:
            skewed_vars[col] = {
                'original_data': data,
                'skewness': skewness,
                'kurtosis': kurtosis,
                'needs_transformation': True
            }
            print(" ← NEEDS TRANSFORMATION")
        else:
            print(" ← OK")

    print(f"\n✓ Found {len(skewed_vars)} variables needing transformation:")
    for var in skewed_vars.keys():
        print(f"  - {var} (skewness: {skewed_vars[var]['skewness']:.3f})")

    return skewed_vars

def test_transformations_for_variable(var_name, data):
    """
    Test both log and Box-Cox transformations for a single variable
    """
    print(f"\n3. Testing transformations for {var_name}...")

    # Ensure positive values for transformations
    if data.min() <= 0:
        data = data + abs(data.min()) + 1e-8
        print(f"   Adjusted data to ensure positive values (min: {data.min():.6f})")

    results = {}

    # Original statistics
    original_stats = {
        'skewness': stats.skew(data),
        'kurtosis': stats.kurtosis(data),
        'shapiro_p': None
    }

    # Shapiro-Wilk test for original
    if len(data) > 5000:
        sample_data = data.sample(5000, random_state=42)
        _, original_stats['shapiro_p'] = shapiro(sample_data)
    else:
        _, original_stats['shapiro_p'] = shapiro(data)

    results['original'] = original_stats

    # Log transformation
    try:
        log_data = np.log(data)
        log_stats = {
            'skewness': stats.skew(log_data),
            'kurtosis': stats.kurtosis(log_data),
            'shapiro_p': None,
            'data': log_data
        }

        # Shapiro-Wilk test for log
        if len(log_data) > 5000:
            sample_data = pd.Series(log_data).sample(5000, random_state=42)
            _, log_stats['shapiro_p'] = shapiro(sample_data)
        else:
            _, log_stats['shapiro_p'] = shapiro(log_data)

        results['log'] = log_stats
        print(f"   Log transformation: skewness={log_stats['skewness']:.4f}")
    except Exception as e:
        print(f"   Log transformation failed: {e}")
        results['log'] = None

    # Box-Cox transformation
    try:
        boxcox_data, lambda_val = boxcox(data)
        boxcox_stats = {
            'skewness': stats.skew(boxcox_data),
            'kurtosis': stats.kurtosis(boxcox_data),
            'shapiro_p': None,
            'lambda': lambda_val,
            'data': boxcox_data
        }

        # Shapiro-Wilk test for Box-Cox
        if len(boxcox_data) > 5000:
            sample_data = pd.Series(boxcox_data).sample(5000, random_state=42)
            _, boxcox_stats['shapiro_p'] = shapiro(sample_data)
        else:
            _, boxcox_stats['shapiro_p'] = shapiro(boxcox_data)

        results['boxcox'] = boxcox_stats
        print(f"   Box-Cox transformation: skewness={boxcox_stats['skewness']:.4f}, λ={lambda_val:.4f}")
    except Exception as e:
        print(f"   Box-Cox transformation failed: {e}")
        results['boxcox'] = None

    return results

def select_best_transformation(var_name, transformation_results):
    """
    Select the best transformation for a variable based on skewness reduction
    """
    print(f"\n4. Selecting best transformation for {var_name}...")

    # Calculate absolute skewness for each transformation
    skewness_comparison = {}

    if transformation_results['original']:
        skewness_comparison['original'] = abs(transformation_results['original']['skewness'])

    if transformation_results['log']:
        skewness_comparison['log'] = abs(transformation_results['log']['skewness'])

    if transformation_results['boxcox']:
        skewness_comparison['boxcox'] = abs(transformation_results['boxcox']['skewness'])

    # Find the transformation with minimum absolute skewness
    best_method = min(skewness_comparison, key=skewness_comparison.get)

    print(f"   Skewness comparison:")
    for method, skew_val in skewness_comparison.items():
        marker = " ← BEST" if method == best_method else ""
        print(f"     {method:8s}: {skew_val:.4f}{marker}")

    # Get transformation details
    transformation_info = {
        'method': best_method,
        'original_skewness': transformation_results['original']['skewness'],
        'transformed_skewness': transformation_results[best_method]['skewness'] if best_method != 'original' else transformation_results['original']['skewness'],
        'improvement': abs(transformation_results['original']['skewness']) - skewness_comparison[best_method]
    }

    if best_method == 'log':
        transformation_info['function'] = 'np.log'
        transformation_info['inverse_function'] = 'np.exp'
        transformation_info['transformed_data'] = transformation_results['log']['data']
    elif best_method == 'boxcox':
        transformation_info['function'] = 'scipy.stats.boxcox'
        transformation_info['lambda'] = transformation_results['boxcox']['lambda']
        transformation_info['transformed_data'] = transformation_results['boxcox']['data']
    else:
        transformation_info['function'] = 'None (original)'
        transformation_info['transformed_data'] = None

    print(f"   Selected: {best_method} (improvement: {transformation_info['improvement']:.4f})")

    return transformation_info

def create_transformation_visualizations(transformation_results, target_var):
    """
    Create visualizations for the most important variables (especially target)
    """
    print("\n5. Creating transformation visualizations...")

    # Focus on target variable and top 3 most skewed variables
    vars_to_plot = [target_var]
    other_vars = [(var, abs(info['original_skewness'])) for var, info in transformation_results.items()
                  if var != target_var and info['method'] != 'original']
    other_vars.sort(key=lambda x: x[1], reverse=True)
    vars_to_plot.extend([var for var, _ in other_vars[:3]])  # Top 3 most skewed

    n_vars = len(vars_to_plot)
    fig, axes = plt.subplots(n_vars, 3, figsize=(15, 4*n_vars))

    if n_vars == 1:
        axes = axes.reshape(1, -1)

    for i, var_name in enumerate(vars_to_plot):
        if var_name not in transformation_results:
            continue

        result = transformation_results[var_name]

        # Get original data (from skewed_vars)
        original_data = None
        for skewed_var, skewed_info in skewed_vars.items():
            if skewed_var == var_name:
                original_data = skewed_info['original_data']
                break

        if original_data is None:
            continue

        # Original distribution
        axes[i, 0].hist(original_data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[i, 0].set_title(f'{var_name} - Original\n(skew: {result["original_skewness"]:.3f})')
        axes[i, 0].set_xlabel(var_name)
        axes[i, 0].set_ylabel('Frequency')

        # Best transformation
        if result['method'] != 'original' and result['transformed_data'] is not None:
            axes[i, 1].hist(result['transformed_data'], bins=30, alpha=0.7,
                          color='lightgreen', edgecolor='black')
            method_label = f"{result['method'].capitalize()}"
            if 'lambda' in result:
                method_label += f" (λ={result['lambda']:.3f})"
            axes[i, 1].set_title(f'{var_name} - {method_label}\n(skew: {result["transformed_skewness"]:.3f})')
            axes[i, 1].set_xlabel(f'{result["method"]}({var_name})')
            axes[i, 1].set_ylabel('Frequency')

            # Q-Q plot of transformed data
            stats.probplot(result['transformed_data'], dist="norm", plot=axes[i, 2])
            axes[i, 2].set_title(f'{var_name} - Q-Q Plot ({result["method"]})')
        else:
            # If no transformation was beneficial
            axes[i, 1].hist(original_data, bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
            axes[i, 1].set_title(f'{var_name} - No Transformation\n(original was best)')
            axes[i, 1].set_xlabel(var_name)
            axes[i, 1].set_ylabel('Frequency')

            stats.probplot(original_data, dist="norm", plot=axes[i, 2])
            axes[i, 2].set_title(f'{var_name} - Q-Q Plot (original)')

    plt.tight_layout()
    plt.savefig('figures/task2_transformation_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✓ Transformation visualizations saved as 'figures/task2_transformation_comparison.png'")

def create_transformation_summary(transformation_results):
    """
    Create a comprehensive summary of all transformations
    """
    print("\n6. Creating transformation summary...")

    # Create summary DataFrame
    summary_data = []
    for var_name, result in transformation_results.items():
        summary_data.append({
            'Variable': var_name,
            'Original_Skewness': result['original_skewness'],
            'Best_Method': result['method'],
            'Transformed_Skewness': result['transformed_skewness'],
            'Improvement': result['improvement'],
            'Lambda': result.get('lambda', 'N/A')
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('Improvement', ascending=False)

    print("\nTransformation Summary:")
    print("=" * 80)
    print(summary_df.round(4))

    # Count transformation methods
    method_counts = summary_df['Best_Method'].value_counts()
    print(f"\nTransformation Methods Used:")
    for method, count in method_counts.items():
        print(f"  {method}: {count} variables")

    return summary_df

def document_all_transformations(transformation_results):
    """
    Document all transformation parameters for reproducibility
    """
    print("\n7. Documenting transformation parameters...")

    transformation_dict = {}

    for var_name, result in transformation_results.items():
        var_info = {
            'original_skewness': result['original_skewness'],
            'transformed_skewness': result['transformed_skewness'],
            'method': result['method'],
            'improvement': result['improvement']
        }

        if result['method'] == 'log':
            var_info.update({
                'function': 'np.log',
                'inverse_function': 'np.exp',
                'formula': f'log({var_name})',
                'inverse_formula': f'exp(log_{var_name})'
            })
        elif result['method'] == 'boxcox':
            lambda_val = result['lambda']
            var_info.update({
                'function': 'scipy.stats.boxcox',
                'lambda': lambda_val,
                'formula': f'({var_name}^{lambda_val:.4f} - 1) / {lambda_val:.4f}' if lambda_val != 0 else f'log({var_name})',
                'inverse_formula': f'(λ * y + 1)^(1/λ) where λ = {lambda_val:.4f}' if lambda_val != 0 else f'exp(y)'
            })
        else:
            var_info.update({
                'function': 'None (original)',
                'note': 'No transformation provided better normality'
            })

        transformation_dict[var_name] = var_info

    print(f"✓ Documented transformations for {len(transformation_dict)} variables")

    return transformation_dict

def main():
    """
    Main function to execute Task 2 - Transform all skewed variables
    """
    global skewed_vars  # Make it accessible for visualization function

    # Load data
    df, target_col = load_data('data.xlsx')

    # Identify all skewed variables
    skewed_vars = identify_skewed_variables(df, skewness_threshold=0.5)

    if not skewed_vars:
        print("No skewed variables found. Task completed.")
        return {}

    # Test transformations for each skewed variable
    transformation_results = {}

    for var_name, var_info in skewed_vars.items():
        print(f"\n{'='*50}")
        print(f"Processing variable: {var_name}")
        print(f"{'='*50}")

        # Test transformations
        test_results = test_transformations_for_variable(var_name, var_info['original_data'])

        # Select best transformation
        best_transform = select_best_transformation(var_name, test_results)

        transformation_results[var_name] = best_transform

    # Create summary
    summary_df = create_transformation_summary(transformation_results)

    # Create visualizations for key variables
    create_transformation_visualizations(transformation_results, target_col)

    # Document all transformations
    transformation_dict = document_all_transformations(transformation_results)

    # Final summary report
    print("\n" + "="*70)
    print("TASK 2 COMPLETION SUMMARY")
    print("="*70)
    print(f"✓ Analyzed {len(df)} observations across {len(df.columns)} variables")
    print(f"✓ Identified {len(skewed_vars)} skewed variables needing transformation")
    print(f"✓ Tested log and Box-Cox transformations for each variable")

    # Count transformation methods
    method_counts = summary_df['Best_Method'].value_counts()
    for method, count in method_counts.items():
        print(f"✓ {method.capitalize()} transformation selected for {count} variables")

    # Show biggest improvements
    top_improvements = summary_df.head(3)
    print(f"\nTop 3 improvements:")
    for _, row in top_improvements.iterrows():
        print(f"  {row['Variable']}: {row['Original_Skewness']:.3f} → {row['Transformed_Skewness']:.3f} ({row['Best_Method']})")

    print("✓ Transformation parameters documented for reproducibility")
    print("✓ Visualizations created and saved")
    print("\nTask 2 completed successfully!")

    return transformation_dict

if __name__ == "__main__":
    transformation_dict = main()
