#!/usr/bin/env python3
"""
Task 4: Categorical Variable Encoding
ML-Based Ksat Prediction Project

This script converts categorical variables (LU and Texture) to numerical format
using one-hot encoding. Also removes PWP feature identified in Task 3 as highly
correlated with clay%.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder, OneHotEncoder
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

def load_data(file_path):
    """
    Load the original dataset
    """
    print("="*60)
    print("TASK 4: CATEGORICAL VARIABLE ENCODING")
    print("="*60)
    
    print("\n1. Loading original data...")
    df = pd.read_excel(file_path)
    print(f"✓ Data loaded: {df.shape}")
    
    # Display all columns
    print(f"\nAll columns ({len(df.columns)}):")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    return df

def remove_correlated_features(df):
    """
    Remove features identified in Task 3 as highly correlated
    Based on Task 3 findings: PWP is highly correlated with clay% (r=0.92)
    """
    print("\n2. Removing highly correlated features...")
    
    # Features to remove based on Task 3 findings
    features_to_remove = ['PWP']  # PWP correlated with clay% (r=0.92)
    
    print(f"Features to remove based on Task 3 multicollinearity analysis:")
    for feature in features_to_remove:
        if feature in df.columns:
            print(f"  - {feature} (highly correlated with other features)")
        else:
            print(f"  - {feature} (not found in dataset)")
    
    # Remove the features
    df_cleaned = df.drop(columns=features_to_remove, errors='ignore')
    
    print(f"\nDataset shape after removing correlated features:")
    print(f"  Before: {df.shape}")
    print(f"  After:  {df_cleaned.shape}")
    print(f"  Removed: {df.shape[1] - df_cleaned.shape[1]} features")
    
    return df_cleaned

def analyze_categorical_variables(df):
    """
    Analyze the categorical variables LU and Texture
    """
    print("\n3. Analyzing categorical variables...")
    
    categorical_vars = ['LU', 'Texture']
    categorical_info = {}
    
    for var in categorical_vars:
        if var in df.columns:
            print(f"\n{var} variable analysis:")
            print("-" * 30)
            
            # Basic info
            unique_values = df[var].unique()
            n_unique = len(unique_values)
            data_type = df[var].dtype
            
            print(f"Data type: {data_type}")
            print(f"Number of unique values: {n_unique}")
            print(f"Missing values: {df[var].isnull().sum()}")
            
            # Value counts
            value_counts = df[var].value_counts().sort_index()
            print(f"\nValue distribution:")
            for value, count in value_counts.items():
                percentage = (count / len(df)) * 100
                # Handle both string and numeric values
                if isinstance(value, str):
                    print(f"  {value:15s}: {count:5d} ({percentage:5.1f}%)")
                else:
                    print(f"  {str(value):15s}: {count:5d} ({percentage:5.1f}%)")
            
            # Store info
            categorical_info[var] = {
                'unique_values': unique_values,
                'n_unique': n_unique,
                'data_type': data_type,
                'value_counts': value_counts,
                'missing_values': df[var].isnull().sum()
            }
        else:
            print(f"\n⚠ Warning: {var} not found in dataset")
    
    return categorical_info

def apply_one_hot_encoding(df, categorical_vars=['LU', 'Texture']):
    """
    Apply one-hot encoding to categorical variables
    """
    print("\n4. Applying one-hot encoding...")
    
    df_encoded = df.copy()
    encoding_info = {}
    
    for var in categorical_vars:
        if var in df.columns:
            print(f"\nEncoding {var}...")
            
            # Get unique values before encoding
            unique_values = sorted(df[var].unique())
            n_categories = len(unique_values)
            
            print(f"  Categories to encode ({n_categories}): {unique_values}")
            
            # Apply one-hot encoding using pandas get_dummies
            # This creates binary columns for each category
            encoded_cols = pd.get_dummies(df[var], prefix=var, prefix_sep='_')
            
            # Add encoded columns to dataframe
            df_encoded = pd.concat([df_encoded, encoded_cols], axis=1)
            
            # Remove original categorical column
            df_encoded = df_encoded.drop(columns=[var])
            
            # Store encoding information
            encoding_info[var] = {
                'original_categories': unique_values,
                'n_categories': n_categories,
                'encoded_columns': list(encoded_cols.columns),
                'n_encoded_columns': len(encoded_cols.columns)
            }
            
            print(f"  ✓ Created {len(encoded_cols.columns)} binary columns")
            print(f"  ✓ Removed original {var} column")
            
        else:
            print(f"\n⚠ Warning: {var} not found in dataset")
    
    return df_encoded, encoding_info

def update_feature_list(df_encoded, encoding_info):
    """
    Create updated feature list with new encoded columns
    """
    print("\n5. Updating feature list...")

    # Get all columns
    all_columns = list(df_encoded.columns)

    # Separate target and features
    target_var = 'Ksat'
    feature_columns = [col for col in all_columns if col != target_var]

    # Categorize features
    numerical_features = []
    encoded_features = []

    for col in feature_columns:
        # Check if it's an encoded categorical feature
        is_encoded = False
        for var, info in encoding_info.items():
            if col in info['encoded_columns']:
                encoded_features.append(col)
                is_encoded = True
                break

        if not is_encoded:
            numerical_features.append(col)

    print(f"\nFeature categorization:")
    print(f"  Target variable: {target_var}")
    print(f"  Numerical features ({len(numerical_features)}):")
    for i, feature in enumerate(numerical_features, 1):
        print(f"    {i:2d}. {feature}")

    print(f"  Encoded categorical features ({len(encoded_features)}):")
    for i, feature in enumerate(encoded_features, 1):
        print(f"    {i:2d}. {feature}")

    print(f"\nTotal features: {len(feature_columns)} (was {len(feature_columns)} after removing PWP)")

    return {
        'all_columns': all_columns,
        'target_variable': target_var,
        'feature_columns': feature_columns,
        'numerical_features': numerical_features,
        'encoded_features': encoded_features
    }

def save_encoded_data(df_encoded, output_path='task4_encoded_data.csv'):
    """
    Save the encoded dataset to CSV file
    """
    print(f"\n6. Saving encoded data...")

    # Save to CSV
    df_encoded.to_csv(output_path, index=False)

    print(f"✓ Encoded data saved as '{output_path}'")
    print(f"  Shape: {df_encoded.shape}")
    print(f"  Columns: {len(df_encoded.columns)}")

    # Display first few rows of encoded data
    print(f"\nFirst 3 rows of encoded data:")
    print(df_encoded.head(3).to_string())

    return output_path

def create_encoding_summary(df_original, df_encoded, categorical_info, encoding_info, feature_info):
    """
    Create comprehensive summary of the encoding process
    """
    print(f"\n7. Creating encoding summary...")

    # Calculate summary statistics
    original_shape = df_original.shape
    encoded_shape = df_encoded.shape

    # Count different types of features
    n_original_features = original_shape[1] - 1  # Exclude target
    n_encoded_features = len(feature_info['feature_columns'])
    n_numerical = len(feature_info['numerical_features'])
    n_categorical_encoded = len(feature_info['encoded_features'])

    # Calculate encoding expansion
    total_categories_encoded = sum([info['n_categories'] for info in encoding_info.values()])
    total_encoded_columns = sum([info['n_encoded_columns'] for info in encoding_info.values()])

    summary = {
        'original_dataset_shape': original_shape,
        'encoded_dataset_shape': encoded_shape,
        'original_features': n_original_features,
        'encoded_features': n_encoded_features,
        'numerical_features': n_numerical,
        'categorical_encoded_features': n_categorical_encoded,
        'categorical_variables_processed': len(encoding_info),
        'total_categories_encoded': total_categories_encoded,
        'total_encoded_columns': total_encoded_columns,
        'features_removed': 1,  # PWP removed
        'encoding_expansion': n_encoded_features - n_original_features + 1  # +1 for PWP removed
    }

    print(f"\nCategorical Encoding Summary:")
    print("=" * 50)
    print(f"Original dataset shape: {summary['original_dataset_shape']}")
    print(f"Encoded dataset shape: {summary['encoded_dataset_shape']}")
    print(f"Original features (excluding target): {summary['original_features']}")
    print(f"Final features (excluding target): {summary['encoded_features']}")
    print(f"  - Numerical features: {summary['numerical_features']}")
    print(f"  - Encoded categorical features: {summary['categorical_encoded_features']}")
    print(f"Categorical variables processed: {summary['categorical_variables_processed']}")
    print(f"Total categories encoded: {summary['total_categories_encoded']}")
    print(f"Features removed (multicollinearity): {summary['features_removed']}")
    print(f"Net feature expansion: {summary['encoding_expansion']}")

    # Detailed encoding information
    print(f"\nDetailed encoding information:")
    for var, info in encoding_info.items():
        print(f"  {var}:")
        print(f"    Original categories: {info['n_categories']}")
        print(f"    Encoded columns: {info['n_encoded_columns']}")
        print(f"    Column names: {info['encoded_columns'][:3]}{'...' if len(info['encoded_columns']) > 3 else ''}")

    return summary

def main():
    """
    Main function to execute Task 4 - Categorical Variable Encoding
    """
    # Load original data
    df_original = load_data('data.xlsx')

    # Remove highly correlated features (PWP based on Task 3)
    df_cleaned = remove_correlated_features(df_original)

    # Analyze categorical variables
    categorical_info = analyze_categorical_variables(df_cleaned)

    # Apply one-hot encoding
    df_encoded, encoding_info = apply_one_hot_encoding(df_cleaned)

    # Update feature list
    feature_info = update_feature_list(df_encoded, encoding_info)

    # Save encoded data
    output_path = save_encoded_data(df_encoded)

    # Create encoding summary
    summary = create_encoding_summary(df_original, df_encoded, categorical_info, encoding_info, feature_info)

    # Final completion report
    print("\n" + "="*60)
    print("TASK 4 COMPLETION SUMMARY")
    print("="*60)
    print(f"✓ Loaded original dataset: {df_original.shape}")
    print(f"✓ Removed PWP feature (highly correlated with clay%)")
    print(f"✓ Analyzed categorical variables: LU and Texture")

    for var, info in categorical_info.items():
        print(f"    - {var}: {info['n_unique']} unique values")

    print(f"✓ Applied one-hot encoding to categorical variables")

    for var, info in encoding_info.items():
        print(f"    - {var}: {info['n_categories']} categories → {info['n_encoded_columns']} binary columns")

    print(f"✓ Updated feature list:")
    print(f"    - Numerical features: {len(feature_info['numerical_features'])}")
    print(f"    - Encoded categorical features: {len(feature_info['encoded_features'])}")
    print(f"    - Total features: {len(feature_info['feature_columns'])}")

    print(f"✓ Saved encoded data as '{output_path}'")
    print(f"    - Final dataset shape: {df_encoded.shape}")

    print("\nTask 4 completed successfully!")

    return {
        'original_data': df_original,
        'encoded_data': df_encoded,
        'categorical_info': categorical_info,
        'encoding_info': encoding_info,
        'feature_info': feature_info,
        'summary': summary,
        'output_path': output_path
    }

if __name__ == "__main__":
    results = main()
