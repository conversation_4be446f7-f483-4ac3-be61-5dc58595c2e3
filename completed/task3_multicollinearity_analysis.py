#!/usr/bin/env python3
"""
Task 3: Multicollinearity Analysis
ML-Based Ksat Prediction Project

This script performs multicollinearity analysis on numerical features to identify
highly correlated variables that should be removed to avoid redundancy.
Excludes Ksat (target), LU, and Texture as specified in task requirements.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

def load_data(file_path):
    """
    Load the dataset and identify variables for correlation analysis
    """
    print("="*60)
    print("TASK 3: MULTICOLLINEARITY ANALYSIS")
    print("="*60)
    
    print("\n1. Loading data...")
    df = pd.read_excel(file_path)
    print(f"✓ Data loaded: {df.shape}")
    
    # Display all columns
    print(f"\nAll columns ({len(df.columns)}):")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    return df

def select_features_for_analysis(df):
    """
    Select numerical features for multicollinearity analysis
    Exclude: Ksat (target), LU (categorical-numeric), Texture (categorical-string)
    """
    print("\n2. Selecting features for multicollinearity analysis...")
    
    # All columns
    all_columns = list(df.columns)
    
    # Variables to exclude as per task requirements
    excluded_vars = ['Ksat', 'LU', 'Texture']
    
    # Select numerical features for analysis
    analysis_features = [col for col in all_columns if col not in excluded_vars]
    
    print(f"\nExcluded variables ({len(excluded_vars)}):")
    for var in excluded_vars:
        if var in all_columns:
            print(f"  - {var} (excluded as per task requirements)")
        else:
            print(f"  - {var} (not found in dataset)")
    
    print(f"\nFeatures selected for correlation analysis ({len(analysis_features)}):")
    for i, var in enumerate(analysis_features, 1):
        print(f"{i:2d}. {var}")
    
    # Extract the selected features
    analysis_df = df[analysis_features].copy()
    
    # Verify all selected features are numerical
    print(f"\nData types of selected features:")
    for var in analysis_features:
        dtype = analysis_df[var].dtype
        print(f"  {var:15s}: {dtype}")
        if dtype not in ['int64', 'float64']:
            print(f"    ⚠ Warning: {var} is not numerical ({dtype})")
    
    return analysis_df, analysis_features

def calculate_correlation_matrix(analysis_df):
    """
    Calculate Pearson correlation matrix for the selected features
    """
    print("\n3. Calculating correlation matrix...")
    
    # Calculate Pearson correlation matrix
    correlation_matrix = analysis_df.corr(method='pearson')
    
    print(f"✓ Correlation matrix calculated for {correlation_matrix.shape[0]} features")
    
    # Display basic statistics about correlations
    # Get upper triangle (excluding diagonal)
    upper_triangle = correlation_matrix.where(
        np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
    )
    
    # Flatten and remove NaN values
    correlations = upper_triangle.stack().values
    
    print(f"\nCorrelation statistics:")
    print(f"  Total feature pairs: {len(correlations)}")
    print(f"  Mean correlation: {np.mean(np.abs(correlations)):.4f}")
    print(f"  Max correlation: {np.max(np.abs(correlations)):.4f}")
    print(f"  Min correlation: {np.min(np.abs(correlations)):.4f}")
    print(f"  Std correlation: {np.std(np.abs(correlations)):.4f}")
    
    return correlation_matrix

def create_correlation_heatmap(correlation_matrix, save_path='figures/task3_correlation_heatmap.png'):
    """
    Create and save correlation heatmap visualization
    """
    print("\n4. Creating correlation heatmap...")
    
    # Set up the matplotlib figure
    plt.figure(figsize=(14, 12))
    
    # Create heatmap
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # Mask upper triangle
    
    heatmap = sns.heatmap(
        correlation_matrix,
        mask=mask,
        annot=True,
        cmap='RdBu_r',
        center=0,
        square=True,
        fmt='.3f',
        cbar_kws={"shrink": .8},
        annot_kws={'size': 8}
    )
    
    plt.title('Correlation Matrix of Numerical Features\n(Excluding Ksat, LU, and Texture)', 
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('Features', fontsize=12)
    plt.ylabel('Features', fontsize=12)
    
    # Rotate labels for better readability
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()  # Don't show, just save as per task requirements
    
    print(f"✓ Correlation heatmap saved as '{save_path}'")

def identify_high_correlations(correlation_matrix, threshold=0.9):
    """
    Identify feature pairs with correlation above the threshold
    """
    print(f"\n5. Identifying feature pairs with |correlation| > {threshold}...")
    
    # Get upper triangle (excluding diagonal) to avoid duplicates
    upper_triangle = correlation_matrix.where(
        np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
    )
    
    # Find high correlations
    high_corr_pairs = []
    
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_value = correlation_matrix.iloc[i, j]
            if abs(corr_value) > threshold:
                feature1 = correlation_matrix.columns[i]
                feature2 = correlation_matrix.columns[j]
                high_corr_pairs.append({
                    'Feature_1': feature1,
                    'Feature_2': feature2,
                    'Correlation': corr_value,
                    'Abs_Correlation': abs(corr_value)
                })
    
    if high_corr_pairs:
        # Convert to DataFrame and sort by absolute correlation
        high_corr_df = pd.DataFrame(high_corr_pairs)
        high_corr_df = high_corr_df.sort_values('Abs_Correlation', ascending=False)
        
        print(f"✓ Found {len(high_corr_pairs)} feature pairs with |correlation| > {threshold}")
        print(f"\nHigh correlation pairs:")
        print("-" * 70)
        for _, row in high_corr_df.iterrows():
            print(f"{row['Feature_1']:15s} ↔ {row['Feature_2']:15s}: {row['Correlation']:7.4f}")
    else:
        high_corr_df = pd.DataFrame()
        print(f"✓ No feature pairs found with |correlation| > {threshold}")
    
    return high_corr_df

def recommend_feature_removal(high_corr_df, correlation_matrix):
    """
    Recommend which features to remove based on correlation analysis
    """
    print(f"\n6. Recommending features for removal...")
    
    if high_corr_df.empty:
        print("✓ No features need to be removed (no high correlations found)")
        return []
    
    # Strategy: For each highly correlated pair, remove the feature that has
    # higher average correlation with all other features
    
    features_to_remove = []
    processed_pairs = set()
    
    print(f"\nAnalyzing each high correlation pair:")
    print("-" * 60)
    
    for _, row in high_corr_df.iterrows():
        feature1 = row['Feature_1']
        feature2 = row['Feature_2']
        pair_key = tuple(sorted([feature1, feature2]))
        
        # Skip if we've already processed this pair
        if pair_key in processed_pairs:
            continue
        
        processed_pairs.add(pair_key)
        
        # Calculate average correlation for each feature with all others
        avg_corr_1 = correlation_matrix[feature1].abs().mean()
        avg_corr_2 = correlation_matrix[feature2].abs().mean()
        
        # Remove the feature with higher average correlation
        if avg_corr_1 > avg_corr_2:
            remove_feature = feature1
            keep_feature = feature2
        else:
            remove_feature = feature2
            keep_feature = feature1
        
        print(f"{feature1} ↔ {feature2} (r={row['Correlation']:.4f})")
        print(f"  {feature1} avg correlation: {avg_corr_1:.4f}")
        print(f"  {feature2} avg correlation: {avg_corr_2:.4f}")
        print(f"  → Recommend removing: {remove_feature}")
        print()
        
        if remove_feature not in features_to_remove:
            features_to_remove.append(remove_feature)
    
    # Remove duplicates and sort
    features_to_remove = sorted(list(set(features_to_remove)))
    
    print(f"Final recommendation:")
    if features_to_remove:
        print(f"✓ Remove {len(features_to_remove)} features to address multicollinearity:")
        for i, feature in enumerate(features_to_remove, 1):
            print(f"  {i}. {feature}")
    else:
        print("✓ No features need to be removed")
    
    return features_to_remove

def create_analysis_summary(correlation_matrix, high_corr_df, features_to_remove, threshold=0.9):
    """
    Create comprehensive summary of the multicollinearity analysis
    """
    print(f"\n7. Creating analysis summary...")
    
    # Calculate summary statistics
    total_features = len(correlation_matrix.columns)
    total_pairs = (total_features * (total_features - 1)) // 2
    high_corr_pairs = len(high_corr_df)
    features_removed = len(features_to_remove)
    remaining_features = total_features - features_removed
    
    # Get correlation distribution
    upper_triangle = correlation_matrix.where(
        np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
    )
    correlations = upper_triangle.stack().values
    
    summary = {
        'total_features_analyzed': total_features,
        'total_feature_pairs': total_pairs,
        'correlation_threshold': threshold,
        'high_correlation_pairs': high_corr_pairs,
        'features_recommended_for_removal': features_removed,
        'remaining_features_after_removal': remaining_features,
        'mean_absolute_correlation': np.mean(np.abs(correlations)),
        'max_absolute_correlation': np.max(np.abs(correlations)),
        'correlation_std': np.std(np.abs(correlations))
    }
    
    print(f"\nMulticollinearity Analysis Summary:")
    print("=" * 50)
    print(f"Total features analyzed: {summary['total_features_analyzed']}")
    print(f"Total feature pairs: {summary['total_feature_pairs']}")
    print(f"Correlation threshold: {summary['correlation_threshold']}")
    print(f"High correlation pairs found: {summary['high_correlation_pairs']}")
    print(f"Features recommended for removal: {summary['features_recommended_for_removal']}")
    print(f"Remaining features after removal: {summary['remaining_features_after_removal']}")
    print(f"Mean |correlation|: {summary['mean_absolute_correlation']:.4f}")
    print(f"Max |correlation|: {summary['max_absolute_correlation']:.4f}")
    print(f"Correlation std: {summary['correlation_std']:.4f}")
    
    return summary

def main():
    """
    Main function to execute Task 3 - Multicollinearity Analysis
    """
    # Load data
    df = load_data('data.xlsx')
    
    # Select features for analysis (exclude Ksat, LU, Texture)
    analysis_df, analysis_features = select_features_for_analysis(df)
    
    # Calculate correlation matrix
    correlation_matrix = calculate_correlation_matrix(analysis_df)
    
    # Create and save correlation heatmap
    create_correlation_heatmap(correlation_matrix)
    
    # Identify high correlations
    high_corr_df = identify_high_correlations(correlation_matrix, threshold=0.9)
    
    # Recommend features for removal
    features_to_remove = recommend_feature_removal(high_corr_df, correlation_matrix)
    
    # Create analysis summary
    summary = create_analysis_summary(correlation_matrix, high_corr_df, features_to_remove)
    
    # Final completion report
    print("\n" + "="*60)
    print("TASK 3 COMPLETION SUMMARY")
    print("="*60)
    print(f"✓ Analyzed {len(analysis_features)} numerical features for multicollinearity")
    print(f"✓ Excluded Ksat (target), LU, and Texture as specified")
    print(f"✓ Created correlation matrix using Pearson correlation")
    print(f"✓ Generated correlation heatmap (saved to figures/)")
    print(f"✓ Identified {len(high_corr_df)} feature pairs with |correlation| > 0.9")
    
    if features_to_remove:
        print(f"✓ Recommended removing {len(features_to_remove)} features:")
        for feature in features_to_remove:
            print(f"    - {feature}")
    else:
        print("✓ No features need to be removed (no high multicollinearity detected)")
    
    print("✓ Analysis completed without applying transformations (as specified)")
    print("✓ Plots saved to disk without displaying (as specified)")
    print("\nTask 3 completed successfully!")
    
    return {
        'correlation_matrix': correlation_matrix,
        'high_correlations': high_corr_df,
        'features_to_remove': features_to_remove,
        'summary': summary
    }

if __name__ == "__main__":
    results = main()
